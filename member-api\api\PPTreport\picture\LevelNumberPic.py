# -*- coding: utf-8 -*-
"""
会员等级人数分析图片生成模块
生成会员等级的历史会员数和期末会员数的柱状图
"""

import datetime
import logging
from typing import Dict, Any, List, Optional
from pathlib import Path

# 设置matplotlib后端（在导入pyplot之前）
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)

class LevelNumberPicGenerator:
    """会员等级人数分析图片生成器"""

    def __init__(self, bid: str, image_manager):
        """
        初始化图片生成器

        Args:
            bid: 品牌ID
            image_manager: 图片管理器实例
        """
        self.bid = bid
        self.image_manager = image_manager

    def _extract_param(self, query_params, key, default=None):
        """
        从查询参数中提取值

        Args:
            query_params: 查询参数（对象或字典）
            key: 参数键
            default: 默认值

        Returns:
            参数值
        """
        if hasattr(query_params, key):
            return getattr(query_params, key)
        elif isinstance(query_params, dict):
            return query_params.get(key, default)
        else:
            return default

    async def generate_level_number_chart(self, query_params) -> Dict[str, str]:
        """
        生成会员等级人数分析图表

        Args:
            query_params: 查询参数（对象或字典）

        Returns:
            Dict: 包含图片路径的字典
        """
        try:
            logger.info(f"开始生成会员等级人数分析图表 - bid: {self.bid}")
            logger.info(f"查询参数类型: {type(query_params)}")
            logger.info(f"图片管理器会话目录: {self.image_manager.session_dir}")

            # 提取查询参数
            start_date = self._extract_param(query_params, 'start_date', '2025-06-01')
            end_date = self._extract_param(query_params, 'end_date', '2025-06-30')
            bid = self._extract_param(query_params, 'bid', self.bid)
            sid = self._extract_param(query_params, 'sid', None)

            logger.info(f"查询时间范围: {start_date} 到 {end_date}")
            logger.info(f"品牌ID: {bid}, 门店ID: {sid}")

            # 计算历史日期和期末日期
            start_date_obj = datetime.datetime.strptime(start_date, "%Y-%m-%d")
            end_date_obj = datetime.datetime.strptime(end_date, "%Y-%m-%d")

            # 历史会员数：查询时间区间前一天
            history_date = (start_date_obj - datetime.timedelta(days=1)).strftime("%Y-%m-%d")
            # 期末会员数：查询时间区间最后一天
            final_date = end_date

            logger.info(f"历史会员数查询日期: {history_date}")
            logger.info(f"期末会员数查询日期: {final_date}")

            # 获取会员等级人数数据
            level_data = await self._fetch_level_number_data(history_date, final_date, bid, sid)

            # 初始化结果字典
            result = {}

            # 如果数据获取失败，生成错误图片
            if not level_data:
                logger.warning("会员等级人数数据获取失败，生成错误图片")
                error_path = self._generate_error_image("level_number", "会员等级人数数据获取失败")
                if error_path:
                    result["level_number"] = error_path
            else:
                # 生成正常图表
                chart_path = await self._generate_chart(
                    level_data,
                    "会员等级人数分析",
                    "level_number"
                )
                if chart_path:
                    result["level_number"] = chart_path

            # 生成AI分析报告（无论数据是否完整都尝试生成分析）
            try:
                from .PictureAi import PictureAiAnalyzer
                ai_analyzer = PictureAiAnalyzer()

                if level_data:
                    # 有数据时生成正常分析
                    ai_analysis = await ai_analyzer.generate_level_number_analysis(level_data, history_date, final_date)
                    result.update(ai_analysis)
                else:
                    # 无数据时生成默认分析
                    result["level_number_analysis_report"] = "1、会员等级人数数据缺失，无法进行详细分析。\n2、建议检查数据收集机制，确保等级人数数据完整性。\n3、可通过其他渠道补充等级人数数据。\n4、建立完善的等级人数数据监控体系。"

                logger.info("会员等级人数AI分析生成完成")
            except Exception as ai_error:
                logger.error(f"生成会员等级人数AI分析失败: {ai_error}")
                result["level_number_analysis_report"] = "1、AI分析系统暂时不可用，请稍后重试。\n2、建议检查AI服务连接状态和配置。\n3、可暂时使用人工分析替代AI分析功能。\n4、联系技术支持解决AI分析问题。"

            logger.info(f"会员等级人数分析图表生成完成，共生成 {len(result)} 个结果")
            return result

        except Exception as e:
            logger.error(f"生成会员等级人数分析图表失败: {e}")
            import traceback
            traceback.print_exc()
            return {}

    def _generate_error_image(self, image_type: str, error_message: str) -> str:
        """
        生成错误提示图片

        Args:
            image_type: 图片类型
            error_message: 错误信息

        Returns:
            str: 图片保存路径
        """
        try:
            # 创建错误图片
            fig, ax = plt.subplots(figsize=(12, 8))
            ax.text(0.5, 0.5, f'数据生成失败\n{error_message}',
                   horizontalalignment='center', verticalalignment='center',
                   fontsize=20, color='red', weight='bold',
                   transform=ax.transAxes)
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.axis('off')

            # 保存图片
            file_path = self.image_manager.get_image_path(image_type)
            plt.savefig(file_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close(fig)

            logger.info(f"错误图片生成完成: {file_path}")
            return file_path

        except Exception as e:
            logger.error(f"生成错误图片失败: {e}")
            return ""

    async def _fetch_level_number_data(self, history_date: str, final_date: str, bid: str, sid: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        获取会员等级人数数据

        Args:
            history_date: 历史日期 (YYYY-MM-DD格式)
            final_date: 期末日期 (YYYY-MM-DD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            List: 会员等级人数数据列表
        """
        try:
            from api.query.MemberLevels.MemberLevelsSql import MemberLevelsSqlQueries
            from core.database import db

            logger.info(f"开始获取会员等级人数数据: 历史日期 {history_date}, 期末日期 {final_date}")

            # 1. 获取历史会员数数据
            history_sql = MemberLevelsSqlQueries.get_member_level_count_sql(history_date, bid, sid)
            history_results = await db.execute_welife_hydb_query(history_sql)
            logger.info(f"历史会员数数据获取完成，共 {len(history_results)} 条记录")

            # 2. 获取期末会员数数据
            final_sql = MemberLevelsSqlQueries.get_member_level_count_sql(final_date, bid, sid)
            final_results = await db.execute_welife_hydb_query(final_sql)
            logger.info(f"期末会员数数据获取完成，共 {len(final_results)} 条记录")

            # 合并数据
            level_data = self._merge_level_number_data(history_results, final_results)

            logger.info(f"会员等级人数数据合并完成，共 {len(level_data)} 个等级")
            return level_data

        except Exception as e:
            logger.error(f"获取会员等级人数数据失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    def _merge_level_number_data(self, history_results: List[Dict], final_results: List[Dict]) -> List[Dict[str, Any]]:
        """
        合并历史和期末会员数数据

        Args:
            history_results: 历史会员数查询结果
            final_results: 期末会员数查询结果

        Returns:
            List: 合并后的会员等级人数数据
        """
        try:
            # 创建字典以便快速查找
            history_dict = {item.get('ccName', '未知等级'): int(item.get('member_count', 0)) for item in history_results}
            final_dict = {item.get('ccName', '未知等级'): int(item.get('member_count', 0)) for item in final_results}

            # 获取所有会员等级名称
            all_levels = set()
            all_levels.update(history_dict.keys())
            all_levels.update(final_dict.keys())

            # 合并数据
            merged_data = []
            for level_name in sorted(all_levels):  # 按名称排序
                merged_data.append({
                    'ccName': level_name,
                    'history_member_count': history_dict.get(level_name, 0),
                    'final_member_count': final_dict.get(level_name, 0)
                })

            logger.info(f"数据合并完成，包含等级: {[item['ccName'] for item in merged_data]}")
            return merged_data

        except Exception as e:
            logger.error(f"合并会员等级人数数据失败: {e}")
            return []

    async def _generate_chart(self, data: List[Dict[str, Any]], title: str, image_type: str) -> str:
        """
        生成会员等级人数分析柱状图

        Args:
            data: 会员等级人数数据
            title: 图表标题
            image_type: 图片类型

        Returns:
            str: 图片保存路径
        """
        try:
            if not data:
                logger.warning("数据为空，生成空数据提示图表")
                return self._generate_empty_data_chart(title, image_type)

            # 提取数据
            level_names = [item['ccName'] for item in data]
            history_counts = [item['history_member_count'] for item in data]
            final_counts = [item['final_member_count'] for item in data]

            logger.info(f"准备生成图表，包含 {len(level_names)} 个会员等级")

            # 创建图表
            fig, ax = plt.subplots(figsize=(14, 8))

            # 设置X轴位置
            x_pos = range(len(level_names))
            x = np.arange(len(level_names))

            # 绘制柱状图
            bar_width = 0.35
            bars1 = ax.bar([x - bar_width/2 for x in x_pos], history_counts,
                          bar_width, label='历史会员数', color='#E74C3C', alpha=0.8)
            bars2 = ax.bar([x + bar_width/2 for x in x_pos], final_counts,
                          bar_width, label='期末会员数', color='#4472C4', alpha=0.8)

            # 设置坐标轴
            ax.set_xlabel('会员卡名称', fontsize=12)
            ax.set_ylabel('会员人数', fontsize=12)
            ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
            ax.set_xticks(x_pos)
            ax.set_xticklabels(level_names, rotation=0, ha='center')
            ax.grid(True, alpha=0.3)

            # 在柱状图上添加数值标签，处理全零数据的情况
            max_history = max(history_counts) if history_counts and max(history_counts) > 0 else 10
            max_final = max(final_counts) if final_counts and max(final_counts) > 0 else 10
            max_count = max(max_history, max_final)

            for i, (history, final) in enumerate(zip(history_counts, final_counts)):
                ax.text(i - bar_width/2, history + max_count * 0.02, f'{history}',
                       ha='center', va='bottom', fontsize=9)
                ax.text(i + bar_width/2, final + max_count * 0.02, f'{final}',
                       ha='center', va='bottom', fontsize=9)

            # 添加图例
            ax.legend(loc='upper left', fontsize=11)

            # 添加数据表格
            self._add_data_table(data, ax)

            # 调整布局
            plt.tight_layout()

            # 保存图片
            file_path = self.image_manager.get_image_path(image_type)
            plt.savefig(file_path, dpi=300, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            plt.close(fig)

            logger.info(f"会员等级人数分析图表生成完成: {file_path}")
            return file_path

        except Exception as e:
            logger.error(f"生成会员等级人数分析图表失败 {image_type}: {e}")
            import traceback
            traceback.print_exc()
            return ""

    def _add_data_table(self, data: List[Dict[str, Any]], ax):
        """
        在图表下方添加数据表格（横向布局，会员等级为横轴）

        Args:
            data: 数据列表
            ax: 坐标轴对象
        """
        try:
            if not data:
                return

            # 准备横向表格数据
            level_names = [item['ccName'] for item in data]
            history_values = [f"{item['history_member_count']:,}" for item in data]
            final_values = [f"{item['final_member_count']:,}" for item in data]

            # 构建横向表格数据：第一行是会员等级名称，后面两行是数据
            table_data = [
                level_names,  # 第一行：会员卡名称
                history_values,  # 第二行：历史会员数
                final_values  # 第三行：期末会员数
            ]

            # 行标题（左侧标签）
            row_labels = ['会员卡名称', '历史会员数(人)', '期末会员数(人)']

            # 创建表格
            table = ax.table(
                cellText=table_data,
                rowLabels=row_labels,
                cellLoc='center',
                loc='bottom',
                bbox=[0, -0.65, 1, 0.45]  # [x, y, width, height] - 与参考文件保持一致
            )

            # 设置表格样式
            table.auto_set_font_size(False)
            table.set_fontsize(8)  # 与参考文件保持一致
            table.scale(1, 1.3)

            # 设置行标题样式（左侧列）
            for i in range(len(row_labels)):
                table[(i, -1)].set_facecolor('#4472C4')
                table[(i, -1)].set_text_props(weight='bold', color='white')

            # 设置数据单元格样式
            for i in range(len(row_labels)):
                for j in range(len(level_names)):
                    if i == 0:  # 会员等级名称行使用浅蓝色
                        table[(i, j)].set_facecolor('#E7F3FF')
                        table[(i, j)].set_text_props(weight='bold')
                    elif i % 2 == 1:  # 奇数行使用浅灰色
                        table[(i, j)].set_facecolor('#F8F9FA')
                    else:  # 偶数行使用白色
                        table[(i, j)].set_facecolor('white')

            logger.info(f"横向数据表格创建完成，包含 {len(level_names)} 个会员等级的数据")

        except Exception as e:
            logger.error(f"添加数据表格失败: {e}")

    def _generate_empty_data_chart(self, title: str, image_type: str) -> str:
        """
        生成空数据提示图表

        Args:
            title: 图表标题
            image_type: 图片类型

        Returns:
            str: 图片保存路径
        """
        try:
            # 创建空数据图表
            fig, ax = plt.subplots(figsize=(14, 8))

            # 显示"暂无数据"提示
            ax.text(0.5, 0.5, '暂无会员等级人数数据\n请检查数据源或调整查询条件',
                   horizontalalignment='center', verticalalignment='center',
                   fontsize=18, color='#666666', weight='bold',
                   transform=ax.transAxes)

            # 设置标题
            ax.set_title(title, fontsize=16, fontweight='bold', pad=20)

            # 隐藏坐标轴
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.axis('off')

            # 保存图片
            save_path = self.image_manager.get_image_path(image_type)
            plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close(fig)

            logger.info(f"空数据图表生成完成: {save_path}")
            return save_path

        except Exception as e:
            logger.error(f"生成空数据图表失败: {e}")
            return ""


# 工厂函数
def create_level_number_pic_generator(bid: str, image_manager) -> LevelNumberPicGenerator:
    """
    创建会员等级人数分析图片生成器

    Args:
        bid: 品牌ID
        image_manager: 图片管理器实例

    Returns:
        LevelNumberPicGenerator: 图片生成器实例
    """
    return LevelNumberPicGenerator(bid, image_manager)